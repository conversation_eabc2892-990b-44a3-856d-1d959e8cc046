[API配置]
请求网址=https://www.dianxiaomi.com/api/popTemuProduct/pageList.json
请求方法=POST

[查询参数]
sortName=2
pageNo=1
pageSize=300
searchType=0
searchValue=
productSearchType=1
shopId=-1
dxmState=offline
dxmOfflineState=publishFail
site=0
fullCid=
sortValue=2
productType=

[请求头]
User-Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
Content-Type=application/x-www-form-urlencoded
Accept=application/json, text/plain, */*
Accept-Language=zh-CN,zh;q=0.9,en;q=0.8
Accept-Encoding=gzip, deflate, br
Connection=keep-alive
Referer=https://www.dianxiaomi.com/

[COOKIE]
MYJ_MKTG_fapsc5t4tc=JTdCJTdE
MYJ_fapsc5t4tc=JTdCJTIyZGV2aWNlSWQlM0ElMjIzNDg3MGQwZC01OTY5LTQ2M2UtOWZhMS0xOTRjZjM1ZjUyNTglMjIlMkMlMjJ1c2VySWQlMjIlM0ElMjIxOTEwMTI3JTIyJTJDJTIycGFyZW50SWQlMjIlM0ElMjIxOTEwMTI3JTIyJTJDJTIyc2Vzc2lvbklkJTIyJTNBMTc0NTAyNDA3MTgyMSUyQyUyMm9wdE91dCUyMiUzQWZhbHNlJTJDJTIybGFzdEV2ZW50SWQlMjIlM0EwJTdE
_clck=e8utqr%%7C2%%7Cfx3%%7C0%%7C1916
dxm_i=MjA4MzA1OSFhVDB5TURnek1EVTUhYzY4N2EzMWFmYWI1YTJhNTdhZjgwNWQ3MjdhYmY0NTk
dxm_t=********************************************************************************
dxm_c=dnk5cHBEN2chWXoxMmVUbHdjRVEzWnchZDlhZTJlOWExN2U0YzkwMDE3NzE3MjdjYjEwMjQ3ZmU
dxm_w=MjZiM2JjZjIyMDhmMTQxMmJhMmRiMjVhNjNmYTI2MWEhZHoweU5tSXpZbU5tTWpJd09HWXhOREV5WW1FeVpHSXlOV0UyTTJaaE1qWXhZUSExM2JlYTk1NzRmNGY4N2IxNmNkNTk1YjJjMzdhNTgzMQ
dxm_s=VkKV0cNq4KLJ7_bU4GKWG3DLh0lw0USnJB7jgLPgvI4
_dxm_ad_client_id=A598CC49F8B80613EF428FD74B7439D2F
Hm_lvt_f8001a3f3d9bf5923f780580eb550c0b=**********,**********,**********,**********
HMACCOUNT=C15F99DB42A7BA7A
JSESSIONID=3DE4C2C41AB6CDEA0891B2884A5B6682
